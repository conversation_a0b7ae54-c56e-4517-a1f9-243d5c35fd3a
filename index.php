<?php
include('session.php');
include 'header.php';
include_once 'db_connection.php';

// Fetch application types from database
$application_types = [];
try {
    $sql_app_types = "SELECT urdu_name, english_name FROM application_types WHERE is_active = 1 ORDER BY english_name";
    $result_app_types = $conn->query($sql_app_types);
    if ($result_app_types && $result_app_types->num_rows > 0) {
        while ($row = $result_app_types->fetch_assoc()) {
            $application_types[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Error fetching application types: " . $e->getMessage());
}

// Fetch priorities from database
$priorities = [];
try {
    $sql_priorities = "SELECT urdu_name, english_name FROM priorities WHERE is_active = 1 ORDER BY id";
    $result_priorities = $conn->query($sql_priorities);
    if ($result_priorities && $result_priorities->num_rows > 0) {
        while ($row = $result_priorities->fetch_assoc()) {
            $priorities[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Error fetching priorities: " . $e->getMessage());
}

// If no data found in lookup tables, use fallback data
if (empty($application_types)) {
    $application_types = [
        ['urdu_name' => 'شناختی کارڈ میں ترمیم', 'english_name' => 'ID Card Modification'],
        ['urdu_name' => 'رینو', 'english_name' => 'Renewal'],
        ['urdu_name' => 'گمشدہ', 'english_name' => 'Lost Card'],
        ['urdu_name' => 'فیملی سرٹیفکیٹ', 'english_name' => 'Family Certificate'],
        ['urdu_name' => 'سمارٹ شناختی کارڈ', 'english_name' => 'Smart ID Card'],
        ['urdu_name' => 'شناختی کارڈمنسوخ', 'english_name' => 'ID Card Cancellation']
    ];
}

if (empty($priorities)) {
    $priorities = [
        ['urdu_name' => 'نارمل', 'english_name' => 'Normal'],
        ['urdu_name' => 'ارجنٹ', 'english_name' => 'Urgent'],
        ['urdu_name' => 'ایگزیکٹیو', 'english_name' => 'Executive']
    ];
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <title>Pak-ID Token Form</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <style>
    body {
      font-family: 'Poppins', sans-serif;
      background-color: #f8f9fa;
    }
    
    .custom-container {
      width: 85%;
      max-width: 1200px;
      margin: 30px auto;
      background: white;
      padding: 30px;
      border-radius: 15px;
      box-shadow: 0 0 20px rgba(0,0,0,0.1);
    }

    .form-title {
      color: #2c3e50;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid #eee;
    }

    .form-group label {
      font-weight: 500;
      color: #34495e;
      margin-bottom: 8px;
    }

    .form-control {
      border-radius: 8px;
      border: 1px solid #ddd;
      padding: 12px 15px;
      height: auto;
      transition: all 0.3s ease;
    }

    .form-control:focus {
      border-color: #3498db;
      box-shadow: 0 0 0 0.2rem rgba(52,152,219,0.25);
    }

    select.form-control {
      padding: 8px 15px;
      height: 45px;
    }

    textarea.form-control {
      min-height: 100px;
    }

    .btn-primary {
      background-color: #3498db;
      border: none;
      padding: 12px 30px;
      font-weight: 500;
      letter-spacing: 0.5px;
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .btn-primary:hover {
      background-color: #2980b9;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(52,152,219,0.3);
    }

    /* Form sections */
    .form-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 25px;
    }

    .form-section-title {
      color: #2c3e50;
      font-size: 1.2em;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 2px solid #e9ecef;
    }

    /* File input styling */
    .custom-file {
      margin-bottom: 15px;
    }

    .custom-file-input:focus ~ .custom-file-label {
      border-color: #3498db;
      box-shadow: 0 0 0 0.2rem rgba(52,152,219,0.25);
    }

    /* Grid layout for form fields */
    .form-row {
      margin-right: -10px;
      margin-left: -10px;
    }

    .form-row > .col,
    .form-row > [class*="col-"] {
      padding-right: 10px;
      padding-left: 10px;
    }

    @font-face {
      font-family: 'JameelNooriNastaleeq';
      src: url('https://urdu.web.pk/fonts/JameelNooriNastaleeq.woff2') format('woff2');
    }

    .urdu-text {
      font-family: 'JameelNooriNastaleeq', serif !important;
      font-size: 16px;
      direction: rtl;
    }

    .input-group {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-items: stretch;
      width: 100%;
    }
    .input-group i {
      display: flex;
      align-items: center;
      padding: 0.375rem 0.75rem;
      font-size: 1rem;
      font-weight: 400;
      line-height: 1.5;
      color: #495057;
      text-align: center;
      white-space: nowrap;
      background-color: #e9ecef;
      border: 1px solid #ced4da;
      border-radius: 0.25rem 0 0 0.25rem;
      border-right: 0;
    }
    .input-group .form-control {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    /* Image preview styling */
    #imagePreview {
      margin-top: 15px;
      text-align: center;
    }

    #imagePreview img {
      max-width: 200px;
      max-height: 200px;
      border-radius: 10px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
      display: none;
    }

    .preview-container {
      position: relative;
      display: inline-block;
    }

    .remove-image {
      position: absolute;
      top: -10px;
      right: -10px;
      background: #ff4444;
      color: white;
      border-radius: 50%;
      width: 25px;
      height: 25px;
      line-height: 25px;
      text-align: center;
      cursor: pointer;
      display: none;
      box-shadow: 0 0 5px rgba(0,0,0,0.2);
    }

    .remove-image:hover {
      background: #cc0000;
    }
  </style>
</head>
<body>

<div class="container custom-container">
  <h2 class="form-title">Token Slip Data Entry</h2>
  <form action="token_data_handle.php" method="post" enctype="multipart/form-data">
    <!-- Basic Information Section -->
    <div class="form-section">
        <h3 class="form-section-title">
            <i class="bi bi-person-vcard"></i>
            Basic Information
        </h3>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="tracking-id">Tracking ID</label>
                    <div class="input-group">
                        <i class="bi bi-upc-scan"></i>
                        <input type="text" class="form-control" id="tracking-id" name="tracking-id" placeholder="Enter tracking ID">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="name">Name</label>
                    <div class="input-group">
                        <i class="bi bi-person"></i>
                        <input type="text" class="form-control" id="name" name="name" placeholder="Enter name">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="cnic-number">CNIC Number</label>
                    <div class="input-group">
                        <i class="bi bi-credit-card"></i>
                        <input type="text" class="form-control" id="cnic-number" name="cnic-number" placeholder="Enter CNIC number" maxlength="15">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="application-type">Application Type</label>
                    <div class="input-group">
                        <i class="bi bi-file-earmark"></i>
                        <select class="form-control urdu-text" id="application-type" name="application-type" required>
                            <option value="">انتخاب کریں - Select Application Type</option>
                            <?php foreach ($application_types as $app_type): ?>
                                <option value="<?php echo htmlspecialchars($app_type['urdu_name']); ?>">
                                    <?php echo htmlspecialchars($app_type['urdu_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Details Section -->
    <div class="form-section">
        <h3 class="form-section-title">
            <i class="bi bi-info-circle"></i>
            Additional Details
        </h3>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label class="form-label" for="priority">Priority</label>
                    <div class="input-group">
                        <i class="bi bi-star"></i>
                        <select class="form-control urdu-text" id="priority" name="priority" required>
                            <option value="">انتخاب کریں - Select Priority</option>
                            <?php foreach ($priorities as $priority): ?>
                                <option value="<?php echo htmlspecialchars($priority['urdu_name']); ?>">
                                    <?php echo htmlspecialchars($priority['urdu_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label class="form-label" for="date">Date</label>
                    <div class="input-group">
                        <i class="bi bi-calendar-date"></i>
                        <input type="date" class="form-control" id="date" name="date">
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label class="form-label" for="time">Time</label>
                    <div class="input-group">
                        <i class="bi bi-clock"></i>
                        <input type="time" class="form-control" id="time" name="time">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="mobile-no">Mobile Number</label>
                    <div class="input-group">
                        <i class="bi bi-phone"></i>
                        <input type="tel" class="form-control" id="mobile-no" name="mobile-no" placeholder="Enter mobile number">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="email-id">Email ID</label>
                    <div class="input-group">
                        <i class="bi bi-envelope"></i>
                        <input type="email" class="form-control" id="email-id" name="email-id" placeholder="Enter email ID">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Family Information Section -->
    <div class="form-section">
        <h3 class="form-section-title">
            <i class="bi bi-people"></i>
            Family Information
        </h3>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="father-name">Father Name</label>
                    <div class="input-group">
                        <i class="bi bi-person"></i>
                        <input type="text" class="form-control" id="father-name" name="father-name" placeholder="Enter father name">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="father-cnic-no">Father CNIC Number</label>
                    <div class="input-group">
                        <i class="bi bi-credit-card"></i>
                        <input type="text" class="form-control" id="father-cnic-no" name="father-cnic-no" placeholder="Enter father CNIC number" maxlength="15">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="mother-name">Mother Name</label>
                    <div class="input-group">
                        <i class="bi bi-person"></i>
                        <input type="text" class="form-control" id="mother-name" name="mother-name" placeholder="Enter mother name">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="mother-cnic-no">Mother CNIC Number</label>
                    <div class="input-group">
                        <i class="bi bi-credit-card"></i>
                        <input type="text" class="form-control" id="mother-cnic-no" name="mother-cnic-no" placeholder="Enter mother CNIC number" maxlength="15">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="spouse-name">Spouse Name</label>
                    <div class="input-group">
                        <i class="bi bi-person"></i>
                        <input type="text" class="form-control" id="spouse-name" name="spouse-name" placeholder="Enter spouse name">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="spouse-cnic-no">Spouse CNIC Number</label>
                    <div class="input-group">
                        <i class="bi bi-credit-card"></i>
                        <input type="text" class="form-control" id="spouse-cnic-no" name="spouse-cnic-no" placeholder="Enter spouse CNIC number" maxlength="15">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Personal Details Section -->
    <div class="form-section">
        <h3 class="form-section-title">
            <i class="bi bi-person-badge"></i>
            Personal Details
        </h3>
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="form-label" for="date-of-birth">Date of Birth</label>
                    <div class="input-group">
                        <i class="bi bi-calendar"></i>
                        <input type="date" class="form-control" id="date-of-birth" name="date-of-birth">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="present-address">Present Address</label>
                    <div class="input-group">
                        <i class="bi bi-house"></i>
                        <textarea class="form-control" id="present-address" name="present-address" rows="3" placeholder="Enter present address"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label" for="permanent-address">Permanent Address</label>
                    <div class="input-group">
                        <i class="bi bi-house-door"></i>
                        <textarea class="form-control" id="permanent-address" name="permanent-address" rows="3" placeholder="Enter permanent address"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="form-label" for="picture">Upload Picture</label>
                    <div class="input-group">
                        <i class="bi bi-camera"></i>
                        <input type="file" class="form-control" id="picture" name="picture" accept="image/*">
                    </div>
                    <div id="imagePreview">
                        <div class="preview-container">
                            <img id="preview" src="#" alt="Preview">
                            <div class="remove-image">&times;</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button type="submit" class="btn btn-primary btn-block">Submit</button>
  </form>

  <!-- Admin Links -->
  <div class="text-center mt-4" style="border-top: 1px solid #eee; padding-top: 20px;">
    <small class="text-muted">
      Admin:
      <a href="manage_lookups.php" class="text-primary" style="text-decoration: none;">
        <i class="bi bi-gear"></i> Manage Application Types & Priorities
      </a>
      |
      <a href="fee.php" class="text-primary" style="text-decoration: none;">
        <i class="bi bi-currency-dollar"></i> Manage Fees
      </a>
      |
      <a href="setup_fee_table.php" class="text-primary" style="text-decoration: none;">
        <i class="bi bi-database"></i> Setup Tables
      </a>
    </small>
  </div>
</div>

<script>
// CNIC input formatting
function formatCNIC(inputElement) {
    inputElement.addEventListener('input', function(event) {
        var value = event.target.value.replace(/[^0-9]/g, '');
        if (value.length > 5) {
            value = value.slice(0, 5) + '-' + value.slice(5);
        }
        if (value.length > 13) {
            value = value.slice(0, 13) + '-' + value.slice(13);
        }
        event.target.value = value;
    });
}

// Apply CNIC formatting to all CNIC fields
['cnic-number', 'father-cnic-no', 'mother-cnic-no', 'spouse-cnic-no'].forEach(function(id) {
    formatCNIC(document.getElementById(id));
});

// Image Preview Functionality
document.getElementById('picture').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('preview');
    const removeBtn = document.querySelector('.remove-image');
    const fileLabel = document.querySelector('.custom-file-label');

    if (file) {
        // Update file input label
        fileLabel.textContent = file.name;

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
            removeBtn.style.display = 'block';
        }
        reader.readAsDataURL(file);
    }
});

// Remove image preview
document.querySelector('.remove-image').addEventListener('click', function() {
    const preview = document.getElementById('preview');
    const fileInput = document.getElementById('picture');
    const fileLabel = document.querySelector('.custom-file-label');
    const removeBtn = document.querySelector('.remove-image');

    preview.src = '#';
    preview.style.display = 'none';
    removeBtn.style.display = 'none';
    fileInput.value = '';
    fileLabel.textContent = 'Choose file';
});

// File input label update
document.querySelector('.custom-file-input').addEventListener('change', function(e) {
    var fileName = e.target.files[0] ? e.target.files[0].name : 'Choose file';
    var nextSibling = e.target.nextElementSibling;
    nextSibling.innerText = fileName;
});
</script>

</body>
</html>
<?php include 'footer.php'; ?>

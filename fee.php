<?php include('session.php'); ?>
<?php include 'header.php'; ?>
<!DOCTYPE html>
<html lang="ur" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فیس اپڈیٹ کریں - Update Fee Record</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts for Urdu -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Nastaliq+Urdu:wght@400;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5530;
            --secondary-color: #4a7c59;
            --accent-color: #f8f9fa;
            --text-dark: #2d3436;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Noto Nastaliq Urdu', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header-section h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .header-section .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 10px;
            position: relative;
            z-index: 1;
        }

        .form-section {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
            outline: none;
        }

        .input-group {
            position: relative;
        }

        .input-group .form-control {
            padding-left: 50px;
        }

        .input-group-text {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--secondary-color);
            z-index: 3;
            font-size: 1.1rem;
        }

        .btn-submit {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(44, 85, 48, 0.3);
            color: white;
        }

        .btn-submit:active {
            transform: translateY(0);
        }

        .btn-submit::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-submit:hover::before {
            left: 100%;
        }

        .auto-calculate {
            background: #f8f9fa;
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            color: var(--secondary-color);
            font-weight: 500;
        }

        .card-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            border-left: 4px solid var(--primary-color);
        }

        .card-info h5 {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 10px;
        }

        .card-info p {
            margin: 0;
            color: var(--text-dark);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header-section {
                padding: 20px;
            }

            .header-section h1 {
                font-size: 2rem;
            }

            .form-section {
                padding: 25px;
            }

            .form-control, .form-select {
                padding: 12px 15px;
            }

            .input-group .form-control {
                padding-left: 45px;
            }
        }

        @media (max-width: 480px) {
            .header-section h1 {
                font-size: 1.8rem;
            }

            .form-section {
                padding: 20px;
            }
        }

        /* Animation */
        .form-group {
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .form-group:nth-child(1) { animation-delay: 0.1s; }
        .form-group:nth-child(2) { animation-delay: 0.2s; }
        .form-group:nth-child(3) { animation-delay: 0.3s; }
        .form-group:nth-child(4) { animation-delay: 0.4s; }
        .form-group:nth-child(5) { animation-delay: 0.5s; }
        .form-group:nth-child(6) { animation-delay: 0.6s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Loading state */
        .btn-submit.loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .btn-submit.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <h1><i class="fas fa-money-bill-wave me-3"></i>فیس اپڈیٹ</h1>
            <p class="subtitle">Update Fee Record - فیس کی معلومات اپڈیٹ کریں</p>
        </div>

        <!-- Form Section -->
        <div class="form-section">
            <div class="card-info">
                <h5><i class="fas fa-info-circle me-2"></i>ہدایات - Instructions</h5>
                <p>مختلف درخواستوں اور ترجیحات کے لیے فیس کی مقدار مقرر کریں۔ کل فیس خودکار طور پر محسوب ہوگی۔</p>
            </div>

            <form action="update_fee.php" method="POST" id="feeForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="application_for" class="form-label">
                                <i class="fas fa-file-alt"></i>
                                درخواست کی قسم - Application Type
                            </label>
                            <select class="form-select" id="application_for" name="application_for" required>
                                <option value="">انتخاب کریں - Select</option>
                                <option value="شناختی کارڈ میں ترمیم">شناختی کارڈ میں ترمیم</option>
                                <option value="رینو">رینو</option>
                                <option value="گمشدہ">گمشدہ</option>
                                <option value="فیملی سرٹیفکیٹ">فیملی سرٹیفکیٹ</option>
                                <option value="سمارٹ شناختی کارڈ">سمارٹ شناختی کارڈ</option>
                                <option value="شناختی کارڈمنسوخ">شناختی کارڈمنسوخ</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="priority" class="form-label">
                                <i class="fas fa-star"></i>
                                ترجیح - Priority
                            </label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="">انتخاب کریں - Select</option>
                                <option value="نارمل">نارمل - Normal</option>
                                <option value="ارجنٹ">ارجنٹ - Urgent</option>
                                <option value="ایگزیکٹیو">ایگزیکٹیو - Executive</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="fee" class="form-label">
                                <i class="fas fa-coins"></i>
                                بنیادی فیس - Basic Fee (PKR)
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">₨</span>
                                <input type="number" class="form-control" id="fee" name="fee"
                                       placeholder="0" min="0" step="0.01" required>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="service_fee" class="form-label">
                                <i class="fas fa-hand-holding-usd"></i>
                                سروس فیس - Service Fee (PKR)
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">₨</span>
                                <input type="number" class="form-control" id="service_fee" name="service_fee"
                                       placeholder="0" min="0" step="0.01" required>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="total_fee" class="form-label">
                        <i class="fas fa-calculator"></i>
                        کل فیس - Total Fee (PKR)
                    </label>
                    <div class="auto-calculate" id="total_fee_display">
                        <i class="fas fa-magic me-2"></i>
                        خودکار حساب کتاب - Auto Calculated: ₨ <span id="calculated_amount">0</span>
                    </div>
                    <input type="hidden" id="total_fee" name="total_fee" value="0">
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-submit" id="submitBtn">
                        <i class="fas fa-save me-2"></i>
                        محفوظ کریں - Save Record
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto calculate total fee
        function calculateTotal() {
            const fee = parseFloat(document.getElementById('fee').value) || 0;
            const serviceFee = parseFloat(document.getElementById('service_fee').value) || 0;
            const total = fee + serviceFee;

            document.getElementById('calculated_amount').textContent = total.toFixed(2);
            document.getElementById('total_fee').value = total.toFixed(2);
        }

        // Add event listeners for auto calculation
        document.getElementById('fee').addEventListener('input', calculateTotal);
        document.getElementById('service_fee').addEventListener('input', calculateTotal);

        // Form submission with loading state
        document.getElementById('feeForm').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>محفوظ ہو رہا ہے...';
        });

        // Initialize calculation on page load
        calculateTotal();
    </script>
</body>
</html>
